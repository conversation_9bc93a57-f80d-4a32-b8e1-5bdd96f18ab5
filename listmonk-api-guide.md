# Listmonk API Guide & Testing Examples

## Overview
Listmonk provides a comprehensive REST API for managing subscribers, lists, campaigns, templates, and transactional emails. All API endpoints require HTTP Basic Authentication.

## Authentication
All API calls require authentication using your admin credentials:
```bash
# Replace 'admin' and 'your_password' with your actual credentials
curl -u 'admin:your_password' 'http://localhost:9000/api/endpoint'
```

## Core API Capabilities

### 1. Subscribers Management

#### Get All Subscribers
```bash
curl -u 'admin:password' 'http://localhost:9000/api/subscribers?page=1&per_page=100'
```

#### Create a New Subscriber
```bash
curl -u 'admin:password' 'http://localhost:9000/api/subscribers' \
  -H 'Content-Type: application/json' \
  --data '{
    "email": "<EMAIL>",
    "name": "Test User",
    "status": "enabled",
    "lists": [1],
    "attribs": {
      "city": "New York",
      "age": 30,
      "interests": ["tech", "marketing"]
    }
  }'
```

#### Query Subscribers with SQL
```bash
curl -u 'admin:password' 'http://localhost:9000/api/subscribers' \
  --url-query "query=subscribers.name LIKE 'John%' AND subscribers.attribs->>'city' = 'New York'"
```

#### Update Subscriber
```bash
curl -u 'admin:password' -X PUT 'http://localhost:9000/api/subscribers/1' \
  -H 'Content-Type: application/json' \
  --data '{
    "name": "Updated Name",
    "attribs": {"city": "San Francisco", "updated": true}
  }'
```

### 2. Lists Management

#### Get All Lists
```bash
curl -u 'admin:password' 'http://localhost:9000/api/lists'
```

#### Create a New List
```bash
curl -u 'admin:password' 'http://localhost:9000/api/lists' \
  -H 'Content-Type: application/json' \
  --data '{
    "name": "Newsletter Subscribers",
    "type": "public",
    "optin": "double",
    "tags": ["newsletter", "marketing"],
    "description": "Main newsletter subscription list"
  }'
```

#### Get Public Lists (No Auth Required)
```bash
curl 'http://localhost:9000/api/public/lists'
```

### 3. Campaigns Management

#### Get All Campaigns
```bash
curl -u 'admin:password' 'http://localhost:9000/api/campaigns?page=1&per_page=50'
```

#### Create a New Campaign
```bash
curl -u 'admin:password' 'http://localhost:9000/api/campaigns' \
  -H 'Content-Type: application/json' \
  --data '{
    "name": "Welcome Campaign",
    "subject": "Welcome to our newsletter!",
    "lists": [1],
    "type": "regular",
    "content_type": "richtext",
    "body": "<h1>Welcome!</h1><p>Thanks for subscribing to our newsletter.</p>",
    "template_id": 1,
    "tags": ["welcome", "onboarding"]
  }'
```

#### Start a Campaign
```bash
curl -u 'admin:password' -X PUT 'http://localhost:9000/api/campaigns/1/status' \
  -H 'Content-Type: application/json' \
  --data '{"status": "running"}'
```

#### Get Campaign Analytics
```bash
# Get view counts
curl -u 'admin:password' 'http://localhost:9000/api/campaigns/analytics/views?id=1&from=2024-01-01&to=2024-12-31'

# Get link clicks
curl -u 'admin:password' 'http://localhost:9000/api/campaigns/analytics/links?id=1&from=2024-01-01&to=2024-12-31'
```

### 4. Templates Management

#### Get All Templates
```bash
curl -u 'admin:password' 'http://localhost:9000/api/templates'
```

#### Create a New Template
```bash
curl -u 'admin:password' 'http://localhost:9000/api/templates' \
  -H 'Content-Type: application/json' \
  --data '{
    "name": "Custom Newsletter Template",
    "type": "campaign",
    "body": "<!DOCTYPE html><html><body><h1>{{ .Campaign.Subject }}</h1>{{ template \"content\" . }}<p>Best regards,<br>The Team</p></body></html>"
  }'
```

#### Preview a Template
```bash
curl -u 'admin:password' 'http://localhost:9000/api/templates/1/preview'
```

### 5. Transactional Emails

#### Send Transactional Email
```bash
curl -u 'admin:password' 'http://localhost:9000/api/tx' \
  -H 'Content-Type: application/json' \
  --data '{
    "subscriber_email": "<EMAIL>",
    "template_id": 2,
    "subject": "Password Reset",
    "data": {
      "reset_link": "https://yoursite.com/reset?token=abc123",
      "user_name": "John Doe",
      "expires_in": "24 hours"
    }
  }'
```

#### Send to Multiple Recipients
```bash
curl -u 'admin:password' 'http://localhost:9000/api/tx' \
  -H 'Content-Type: application/json' \
  --data '{
    "subscriber_emails": ["<EMAIL>", "<EMAIL>"],
    "template_id": 3,
    "data": {"announcement": "New feature released!"}
  }'
```

### 6. Public Subscription (No Auth Required)

#### Subscribe User to Lists
```bash
# JSON format
curl 'http://localhost:9000/api/public/subscription' \
  -H 'Content-Type: application/json' \
  --data '{
    "email": "<EMAIL>",
    "name": "New User",
    "list_uuids": ["your-list-uuid-here"]
  }'

# Form format
curl 'http://localhost:9000/api/public/subscription' \
  -d 'email=<EMAIL>' \
  -d 'name=New User' \
  -d 'l=your-list-uuid-here'
```

## Advanced Features

### Bulk Operations
```bash
# Add multiple subscribers to lists
curl -u 'admin:password' -X PUT 'http://localhost:9000/api/subscribers/lists' \
  -H 'Content-Type: application/json' \
  --data '{
    "ids": [1, 2, 3, 4, 5],
    "action": "add",
    "target_list_ids": [2, 3],
    "status": "confirmed"
  }'

# Bulk delete subscribers by query
curl -u 'admin:password' -X POST 'http://localhost:9000/api/subscribers/query/delete' \
  -H 'Content-Type: application/json' \
  --data '{
    "query": "subscribers.status = '\''blocklisted'\'' AND subscribers.created_at < '\''2023-01-01'\''"
  }'
```

### Bounce Management
```bash
# Get subscriber bounces
curl -u 'admin:password' 'http://localhost:9000/api/subscribers/1/bounces'

# Delete subscriber bounces
curl -u 'admin:password' -X DELETE 'http://localhost:9000/api/subscribers/1/bounces'
```

## Response Format
All API responses follow this structure:
```json
{
  "data": {
    // Response data here
  }
}
```

For paginated results:
```json
{
  "data": {
    "results": [...],
    "total": 150,
    "per_page": 20,
    "page": 1
  }
}
```

## Error Handling
Errors return appropriate HTTP status codes with JSON error messages:
```json
{
  "message": "Error description",
  "type": "error_type"
}
```

## Rate Limits
- No explicit rate limits mentioned in documentation
- Recommended to implement reasonable delays between bulk operations
- Monitor server performance when making many concurrent requests

## Next Steps
1. Test basic subscriber operations
2. Create and manage lists
3. Set up templates for campaigns and transactional emails
4. Test campaign creation and sending
5. Implement transactional email workflows
6. Set up public subscription forms
