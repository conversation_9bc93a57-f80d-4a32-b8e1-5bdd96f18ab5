# Listmonk API Testing Script
# PowerShell script to test various Listmonk API endpoints

param(
    [string]$BaseUrl = "http://localhost:9000",
    [string]$Username = "admin",
    [string]$Password = "listmonk"
)

# Create base64 encoded credentials for Basic Auth
$credentials = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("${Username}:${Password}"))
$headers = @{
    "Authorization" = "Basic $credentials"
    "Content-Type" = "application/json"
}

Write-Host "🚀 Testing Listmonk API at $BaseUrl" -ForegroundColor Green
Write-Host "=" * 50

# Test 1: Get all subscribers
Write-Host "`n📋 Test 1: Getting all subscribers..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/subscribers?per_page=5" -Headers $headers -Method GET
    Write-Host "✅ Success! Found $($response.data.total) total subscribers" -ForegroundColor Green
    if ($response.data.results.Count -gt 0) {
        Write-Host "   First subscriber: $($response.data.results[0].email)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Get all lists
Write-Host "`n📝 Test 2: Getting all lists..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/lists" -Headers $headers -Method GET
    Write-Host "✅ Success! Found $($response.data.results.Count) lists" -ForegroundColor Green
    $global:firstListId = $response.data.results[0].id
    Write-Host "   First list: '$($response.data.results[0].name)' (ID: $global:firstListId)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Create a test subscriber
Write-Host "`n👤 Test 3: Creating a test subscriber..." -ForegroundColor Yellow
$testEmail = "test-$(Get-Date -Format 'yyyyMMdd-HHmmss')@example.com"
$subscriberData = @{
    email = $testEmail
    name = "Test User $(Get-Date -Format 'HH:mm:ss')"
    status = "enabled"
    lists = @($global:firstListId)
    attribs = @{
        city = "Test City"
        source = "API Test"
        created_via = "PowerShell Script"
    }
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/subscribers" -Headers $headers -Method POST -Body $subscriberData
    Write-Host "✅ Success! Created subscriber with ID: $($response.data.id)" -ForegroundColor Green
    Write-Host "   Email: $($response.data.email)" -ForegroundColor Cyan
    $global:testSubscriberId = $response.data.id
} catch {
    Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Get public lists (no auth required)
Write-Host "`n🌐 Test 4: Getting public lists (no authentication)..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/public/lists" -Method GET
    Write-Host "✅ Success! Found $($response.Count) public lists" -ForegroundColor Green
    if ($response.Count -gt 0) {
        Write-Host "   First public list: '$($response[0].name)'" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Get all templates
Write-Host "`n🎨 Test 5: Getting all templates..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/templates" -Headers $headers -Method GET
    Write-Host "✅ Success! Found $($response.data.Count) templates" -ForegroundColor Green
    if ($response.data.Count -gt 0) {
        $global:firstTemplateId = $response.data[0].id
        Write-Host "   First template: '$($response.data[0].name)' (ID: $global:firstTemplateId)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Get all campaigns
Write-Host "`n📧 Test 6: Getting all campaigns..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/campaigns?per_page=5" -Headers $headers -Method GET
    Write-Host "✅ Success! Found $($response.data.total) total campaigns" -ForegroundColor Green
    if ($response.data.results.Count -gt 0) {
        Write-Host "   Latest campaign: '$($response.data.results[0].name)'" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 7: Create a test campaign (draft)
Write-Host "`n📬 Test 7: Creating a test campaign..." -ForegroundColor Yellow
$campaignData = @{
    name = "API Test Campaign $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
    subject = "Test Email from API"
    lists = @($global:firstListId)
    type = "regular"
    content_type = "richtext"
    body = "<h1>Hello from API!</h1><p>This is a test campaign created via the Listmonk API at $(Get-Date).</p><p>Best regards,<br>The API Test Script</p>"
    template_id = $global:firstTemplateId
    tags = @("api-test", "automation")
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/campaigns" -Headers $headers -Method POST -Body $campaignData
    Write-Host "✅ Success! Created campaign with ID: $($response.data.id)" -ForegroundColor Green
    Write-Host "   Campaign: '$($response.data.name)'" -ForegroundColor Cyan
    Write-Host "   Status: $($response.data.status)" -ForegroundColor Cyan
    $global:testCampaignId = $response.data.id
} catch {
    Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 8: Send a transactional email (if we have a transactional template)
Write-Host "`n💌 Test 8: Testing transactional email..." -ForegroundColor Yellow
$txData = @{
    subscriber_email = $testEmail
    template_id = $global:firstTemplateId
    subject = "Test Transactional Email"
    data = @{
        user_name = "Test User"
        message = "This is a test transactional email sent via API"
        timestamp = (Get-Date).ToString()
    }
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/tx" -Headers $headers -Method POST -Body $txData
    if ($response.data -eq $true) {
        Write-Host "✅ Success! Transactional email sent" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Response received but status unclear" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Note: This might fail if template is not configured for transactional emails" -ForegroundColor Gray
}

# Test 9: Query subscribers with SQL
Write-Host "`n🔍 Test 9: Querying subscribers with SQL..." -ForegroundColor Yellow
$query = "subscribers.email LIKE '%@example.com'"
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/subscribers?query=$([System.Web.HttpUtility]::UrlEncode($query))" -Headers $headers -Method GET
    Write-Host "✅ Success! Found $($response.data.total) subscribers matching query" -ForegroundColor Green
    Write-Host "   Query: $query" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 10: Get subscriber details
if ($global:testSubscriberId) {
    Write-Host "`n👁️  Test 10: Getting subscriber details..." -ForegroundColor Yellow
    try {
        $response = Invoke-RestMethod -Uri "$BaseUrl/api/subscribers/$global:testSubscriberId" -Headers $headers -Method GET
        Write-Host "✅ Success! Retrieved subscriber details" -ForegroundColor Green
        Write-Host "   Email: $($response.data.email)" -ForegroundColor Cyan
        Write-Host "   Status: $($response.data.status)" -ForegroundColor Cyan
        Write-Host "   Lists: $($response.data.lists.Count)" -ForegroundColor Cyan
    } catch {
        Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n" + "=" * 50
Write-Host "🎉 API Testing Complete!" -ForegroundColor Green
Write-Host "`nSummary of created test data:" -ForegroundColor Yellow
if ($global:testSubscriberId) { Write-Host "   • Test Subscriber ID: $global:testSubscriberId ($testEmail)" -ForegroundColor Cyan }
if ($global:testCampaignId) { Write-Host "   • Test Campaign ID: $global:testCampaignId" -ForegroundColor Cyan }
Write-Host "`nYou can clean up test data manually from the Listmonk dashboard." -ForegroundColor Gray
